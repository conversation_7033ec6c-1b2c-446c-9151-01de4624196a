<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Assessment Manager</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.23.4/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      @keyframes slideIn {
        from {
          transform: translateY(-20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      .fade-in {
        animation: fadeIn 0.3s ease-out;
      }
      .slide-in {
        animation: slideIn 0.3s ease-out;
      }
      .hover-scale {
        transition: transform 0.2s ease;
      }
      .hover-scale:hover {
        transform: scale(1.05);
      }
      .modal-overlay {
        backdrop-filter: blur(8px);
        background: rgba(0, 0, 0, 0.6);
      }
      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4f46e5;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .option-label input:checked + span {
        background: #dbeafe;
        font-weight: 500;
        border-radius: 8px;
        padding: 8px;
      }
      .tab-active {
        border-bottom: 2px solid #4f46e5;
        color: #4f46e5;
        font-weight: 600;
      }
      .unanswered-question {
        border-left: 4px solid #ef4444;
        background-color: #fef2f2;
        padding-left: 12px;
      }
      .missing-answer {
        color: #ef4444;
        font-weight: 500;
      }
      .warning-icon {
        color: #ef4444;
        margin-right: 4px;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen font-sans">
    <div id="root"></div>

    <script type="text/babel">
      const API_BASE_URL = "http://127.0.0.1:8099/api/v1";
      const AUTH_URL = "http://127.0.0.1:8099/api/v1/login";

      const ASSESSMENT_TEMPLATE = {
        code: "MOCK",
        name: "Untitled Assessment",
        version_int: 1,
        dimensions: [
          { dimension_id: null, name: "TRUE_FALSE", code: "TF", sequence: 0 },
          {
            dimension_id: null,
            name: "SINGLE_CHOICE",
            code: "SC",
            sequence: 1,
          },
          {
            dimension_id: null,
            name: "MULTIPLE_CHOICE",
            code: "MC",
            sequence: 2,
          },
        ],
        questions: [],
      };

      const COURSE_TEMPLATE = {
        title: "",
        description: "",
        is_active: true,
        lessons: [],
      };
      const LESSON_TEMPLATE = {
        title: "",
        body_md: "",
        video_url: "",
        file_url: "",
        content_type: "TEXT",
      };

      const COURSE_CONTENT_TEMPLATE = {
        content_type: "TEXT",
        sequence: 0,
        lesson_id: null,
        assessment_id: null,
      };

      const ASSESSMENT_TYPES = ["MOCK", "REAL", "QUIZ"];
      const DIMENSION_TYPES = [
        "TRUE_FALSE",
        "SINGLE_CHOICE",
        "MULTIPLE_CHOICE",
      ];
      const DIMENSION_CODES = ["TF", "SC", "MC"];
      const CONTENT_TYPES = ["text", "video", "assessment"];
      const LESSON_CONTENT_TYPES = ["TEXT", "VIDEO"];
      const OPTION_TEMPLATE = {
        option_id: null,
        label: "",
        value: null,
        is_correct: false,
      };

      // Axios interceptor to attach Bearer token
      axios.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem("access_token");
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
        (error) => Promise.reject(error)
      );

      // Handle 401 unauthorized responses
      axios.interceptors.response.use(
        (response) => response,
        (error) => {
          if (error.response?.status === 401) {
            localStorage.removeItem("access_token");
            window.location.reload();
          }
          return Promise.reject(error);
        }
      );

      const LoginModal = ({ onLogin }) => {
        const [username, setUsername] = React.useState("");
        const [password, setPassword] = React.useState("");
        const [isLoading, setIsLoading] = React.useState(false);
        const [error, setError] = React.useState("");

        const handleSubmit = async (e) => {
          e.preventDefault();
          if (!validateForm()) return;

          setIsLoading(true);
          try {
            // Base assessment payload for create/update
            const basePayload = {
              name: formData.name,
              code: formData.code, // "MOCK" | "REAL" | "QUIZ"
              version_int:
                formData.version_int === ""
                  ? 0
                  : parseInt(formData.version_int, 10),
            };

            if (assessment?.assessment_id) {
              // ===== UPDATE ASSESSMENT (flat API) =====
              const aid = assessment.assessment_id;

              // Update assessment meta
              await axios.put(
                `${API_BASE_URL}/assessments/${aid}`,
                basePayload
              );

              // Delete removed entities
              for (const dimensionId of removedDimensions) {
                await axios.delete(`${API_BASE_URL}/dimensions/${dimensionId}`);
              }
              for (const questionId of removedQuestions) {
                await axios.delete(`${API_BASE_URL}/questions/${questionId}`);
              }
              for (const optionId of removedOptions) {
                await axios.delete(`${API_BASE_URL}/options/${optionId}`);
              }

              // Upsert dimensions (build code->id for questions)
              const codeToId = {};
              for (const dim of formData.dimensions) {
                const dimPayload = {
                  name: dim.name,
                  code: dim.code,
                  sequence:
                    dim.sequence === "" || dim.sequence == null
                      ? 0
                      : parseInt(dim.sequence, 10),
                };
                if (dim.dimension_id) {
                  await axios.put(
                    `${API_BASE_URL}/dimensions/${dim.dimension_id}`,
                    dimPayload
                  );
                  codeToId[dim.code] = dim.dimension_id;
                } else {
                  const d = await axios.post(
                    `${API_BASE_URL}/assessments/${aid}/dimensions`,
                    dimPayload
                  );
                  codeToId[dim.code] = d.data.dimension_id;
                }
              }

              // Upsert questions & options
              for (const q of formData.questions) {
                const dimension_id = codeToId[q.dimension_code];
                const qPayload = {
                  body_md: q.body_md,
                  dimension_id,
                  sequence:
                    q.sequence === "" || q.sequence == null
                      ? 0
                      : parseInt(q.sequence, 10),
                  block_label: q.block_label || null,
                };

                if (q.question_id) {
                  await axios.put(
                    `${API_BASE_URL}/questions/${q.question_id}`,
                    qPayload
                  );

                  for (const opt of q.options) {
                    const optPayload = {
                      label: opt.label,
                      // ⬇⬇⬇ key change: value must be integer or null
                      value:
                        opt.value === "" || opt.value === null
                          ? null
                          : parseInt(opt.value, 10),
                      is_correct: !!opt.is_correct,
                    };

                    if (opt.option_id) {
                      await axios.put(
                        `${API_BASE_URL}/options/${opt.option_id}`,
                        optPayload
                      );
                    } else {
                      const o = await axios.post(
                        `${API_BASE_URL}/questions/${q.question_id}/options`,
                        optPayload
                      );
                      opt.option_id = o.data.option_id;
                    }
                  }
                } else {
                  const qRes = await axios.post(
                    `${API_BASE_URL}/assessments/${aid}/questions`,
                    qPayload
                  );
                  const newQid = qRes.data.question_id;

                  for (const opt of q.options) {
                    const o = await axios.post(
                      `${API_BASE_URL}/questions/${newQid}/options`,
                      {
                        label: opt.label,
                        value:
                          opt.value === "" || opt.value === null
                            ? null
                            : parseInt(opt.value, 10),
                        is_correct: !!opt.is_correct,
                      }
                    );
                    opt.option_id = o.data.option_id;
                  }
                }
              }

              setNotification?.({
                message: "Assessment updated successfully!",
                type: "success",
              });
              onSave?.("Assessment updated successfully!", "success");
            } else {
              // ===== CREATE ASSESSMENT (nested) =====
              const payload = {
                ...basePayload,
                dimensions: formData.dimensions.map((d, idx) => ({
                  name: d.name,
                  code: d.code,
                  sequence:
                    d.sequence === "" || d.sequence == null
                      ? idx
                      : parseInt(d.sequence, 10),
                  value: d.value ?? null,
                })),
                questions: formData.questions.map((q, qIdx) => ({
                  body_md: q.body_md,
                  dimension_code: q.dimension_code,
                  sequence:
                    q.sequence === "" || q.sequence == null
                      ? qIdx
                      : parseInt(q.sequence, 10),
                  block_label: q.block_label || null,
                  options: (q.options || []).map((o) => ({
                    label: o.label,
                    // ⬇⬇⬇ key change: value must be string or null
                    value:
                      o.value === "" || o.value === null
                        ? null
                        : String(o.value),
                    is_correct: !!o.is_correct,
                  })),
                })),
              };

              await axios.post(`${API_BASE_URL}/assessments`, payload);

              setNotification?.({
                message: "Assessment created successfully!",
                type: "success",
              });
              onSave?.("Assessment created successfully!", "success");
            }
          } catch (error) {
            console.error("Error saving assessment:", error);
            const raw =
              error?.response?.data?.detail ?? "Failed to save assessment";
            const msg =
              typeof raw === "string"
                ? raw
                : raw?.msg || raw?.message || JSON.stringify(raw);
            setErrors((prev) => ({ ...prev, submit: msg }));
            setNotification?.({ message: msg, type: "error" });
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-md w-full shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">Login</h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    disabled={isLoading}
                    required
                  />
                </div>
                {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center ${
                    isLoading ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {isLoading ? (
                    <div className="spinner mr-2"></div>
                  ) : (
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M11 16l-4-4m0 0l4-4m-4 4h14"
                      />
                    </svg>
                  )}
                  Login
                </button>
              </form>
            </div>
          </div>
        );
      };
const CourseForm = ({ course, onSave, onCancel, assessments }) => {
  const [formData, setFormData] = React.useState(
    course || { title: "", description: "", is_active: true }
  );
  const [errors, setErrors] = React.useState({});
  const [isLoading, setIsLoading] = React.useState(false);

  const validate = () => {
    const e = {};
    if (!formData.title?.trim()) e.title = "Title is required";
    setErrors(e);
    return Object.keys(e).length === 0;
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    setErrors((prev) => ({ ...prev, [name]: null }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;

    setIsLoading(true);
    try {
      const payload = {
        title: formData.title,
        description: formData.description || "",
        is_active: !!formData.is_active,
      };

      if (course?.course_id) {
        await axios.put(`${API_BASE_URL}/courses/${course.course_id}`, payload);
        onSave({ ...course, ...payload });
      } else {
        await axios.post(`${API_BASE_URL}/courses`, payload);
        onSave({ ...payload });
      }
    } catch (err) {
      const msg = err?.response?.data?.detail || "Failed to save course";
      onSave({ error: msg }, "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <h2 className="text-3xl font-bold mb-6 text-gray-800">
          {course ? "Edit Course" : "Create Course"}
        </h2>

        <form onSubmit={handleSubmit}>
          <div className="mb-5">
            <label className="block text-gray-700 font-medium mb-1">Title</label>
            <input
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                errors.title ? "border-red-500" : "border-gray-300"
              }`}
              disabled={isLoading}
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title}</p>
            )}
          </div>

          <div className="mb-5">
            <label className="block text-gray-700 font-medium mb-1">Description</label>
            <textarea
              name="description"
              value={formData.description || ""}
              onChange={handleChange}
              rows={4}
              className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
              disabled={isLoading}
            />
          </div>

          <label className="flex items-center gap-2 mb-6">
            <input
              type="checkbox"
              name="is_active"
              checked={!!formData.is_active}
              onChange={handleChange}
              disabled={isLoading}
            />
            <span>Active</span>
          </label>

          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              disabled={isLoading}
            >
              {isLoading && <div className="spinner mr-2" />}
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

      const Notification = ({ message, type, onClose, details }) => {
        const toText = (v) => {
          if (v == null) return "";
          if (
            typeof v === "string" ||
            typeof v === "number" ||
            typeof v === "boolean"
          )
            return String(v);
          if (Array.isArray(v)) return v.map(toText).join(", ");
          // try common FastAPI/validation shapes
          if (v.msg) return String(v.msg);
          return JSON.stringify(v, null, 2);
        };

        return (
          <div
            className={`slide-in fixed top-4 right-4 p-4 rounded-lg shadow-lg text-white ${
              type === "success" ? "bg-green-500" : "bg-red-500"
            } flex flex-col max-w-md whitespace-pre-wrap`}
          >
            <div className="flex justify-between items-start">
              <span>{toText(message)}</span>
              <button
                onClick={onClose}
                className="ml-4 text-white hover:text-gray-200"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            {details && (
              <div className="mt-2 text-sm bg-white bg-opacity-20 p-2 rounded">
                {Array.isArray(details) ? (
                  <ul className="list-disc pl-5">
                    {details.map((detail, i) => (
                      <li key={i}>{toText(detail)}</li>
                    ))}
                  </ul>
                ) : (
                  <p>{toText(details)}</p>
                )}
              </div>
            )}
          </div>
        );
      };

      const AssessmentCard = ({
        assessment,
        onEdit,
        onDelete,
        onViewAsStudent,
        onStartSession,
      }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            {assessment.name}
          </h3>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Code:</span> {assessment.code}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Version:</span>{" "}
            {assessment.version_int}
          </p>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onEdit(assessment)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit
            </button>
            <button
              onClick={() => onDelete(assessment.assessment_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
            <button
              onClick={() =>
                onViewAsStudent(assessment.assessment_id, assessment.name)
              }
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm6 0c0 3.18-1.35 6.06-3.52 8.07a16.84 16.84 0 01-11.96 0C3.35 18.06 2 15.18 2 12s1.35-6.06 3.52-8.07a16.84 16.84 0 0111.96 0C19.65 5.94 21 8.82 21 12z"
                />
              </svg>
              View as Student
            </button>
            <button
              onClick={() =>
                onStartSession(assessment.assessment_id, assessment.name)
              }
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 2m6-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Start Session
            </button>
          </div>
        </div>
      );

      const SessionCard = ({
        session,
        onView,
        onDelete,
        onSubmitResponses,
        onViewResults,
      }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-2">
            Session ID: {session.session_id}
          </h3>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Assessment ID:</span>{" "}
            {session.assessment_id}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Started:</span>{" "}
            {new Date(session.started_at).toLocaleString()}
          </p>
          <p className="text-gray-600 mb-4">
            <span className="font-semibold">Completed:</span>{" "}
            {session.completed_at
              ? new Date(session.completed_at).toLocaleString()
              : "Not completed"}
          </p>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onView(session.session_id)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm6 0c0 3.18-1.35 6.06-3.52 8.07a16.84 16.84 0 01-11.96 0C3.35 18.06 2 15.18 2 12s1.35-6.06 3.52-8.07a16.84 16.84 0 0111.96 0C19.65 5.94 21 8.82 21 12z"
                />
              </svg>
              View
            </button>
            <button
              onClick={() => onDelete(session.session_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
            {!session.completed_at && (
              <button
                onClick={() =>
                  onSubmitResponses(session.session_id, session.assessment_id)
                }
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 3"
                  />
                </svg>
                Submit Responses
              </button>
            )}
            {session.completed_at && (
              <button
                onClick={() => onViewResults(session.session_id)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2-12H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2z"
                  />
                </svg>
                View Results
              </button>
            )}
          </div>
        </div>
      );

      const CourseCard = ({
        course,
        onEdit,
        onDelete,
        onManageLessons,
        onManageContent,
      }) => (
        <div className="fade-in bg-white rounded-xl shadow-xl p-6 mb-6 hover-scale border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            {course.title}
          </h3>
          <p className="text-gray-600 mb-2">
            {course.description || "No description provided"}
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Status:</span>
            <span
              className={`ml-2 px-2 py-1 rounded-full text-xs ${
                course.is_active
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}
            >
              {course.is_active ? "Active" : "Inactive"}
            </span>
          </p>
          <p className="text-gray-600 mb-1">
            <span className="font-semibold">Created:</span>{" "}
            {new Date(course.created_at).toLocaleDateString()}
          </p>
          <p className="text-gray-600 mb-4">
            <span className="font-semibold">Content Items:</span>{" "}
            {course.contents?.length || 0}
          </p>

          {course.contents && course.contents.length > 0 && (
            <div className="mb-4">
              <h4 className="font-semibold text-gray-700 mb-2">
                Course Content:
              </h4>
              <div className="space-y-1">
                {course.contents.slice(0, 3).map((content, index) => (
                  <div
                    key={content.content_id}
                    className="text-sm text-gray-600 flex items-center"
                  >
                    <span className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs mr-2">
                      {content.sequence + 1}
                    </span>
                    <span className="capitalize">
                      {content.content_type.toLowerCase()}
                    </span>
                    {content.lesson && (
                      <span className="ml-2">- {content.lesson.title}</span>
                    )}
                    {content.assessment && (
                      <span className="ml-2">- {content.assessment.name}</span>
                    )}
                  </div>
                ))}
                {course.contents.length > 3 && (
                  <div className="text-sm text-gray-500">
                    ... and {course.contents.length - 3} more items
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => onEdit(course)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit Course
            </button>
            <button
              onClick={() => onManageLessons(course)}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
              Add Lesson
            </button>
            <button
              onClick={() => onManageContent(course)}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              Manage Content
            </button>
            <button
              onClick={() => onDelete(course.course_id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4m-4 0h4m-8 4h12"
                />
              </svg>
              Delete
            </button>
          </div>
        </div>
      );

      const SessionViewModal = ({ sessionId, onClose, setNotification }) => {
        const [session, setSession] = React.useState(null);
        const [responses, setResponses] = React.useState([]);
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchSessionData = async () => {
            setIsLoading(true);
            try {
              const [sessionRes, responsesRes] = await Promise.all([
                axios.get(`${API_BASE_URL}/sessions/${sessionId}`),
                axios.get(`${API_BASE_URL}/sessions/${sessionId}/response`),
              ]);
              setSession(sessionRes.data);
              setResponses(responsesRes.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load session data",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchSessionData();
        }, [sessionId]);

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Session Details
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : !session ? (
                <p className="text-gray-600">No session data available.</p>
              ) : (
                <div className="space-y-6">
                  <div>
                    <p className="text-gray-600">
                      <span className="font-semibold">Session ID:</span>{" "}
                      {session.session_id}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Assessment ID:</span>{" "}
                      {session.assessment_id}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Started:</span>{" "}
                      {new Date(session.started_at).toLocaleString()}
                    </p>
                    <p className="text-gray-600">
                      <span className="font-semibold">Completed:</span>{" "}
                      {session.completed_at
                        ? new Date(session.completed_at).toLocaleString()
                        : "Not completed"}
                    </p>
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-gray-800">
                    Responses
                  </h3>
                  {responses.length === 0 ? (
                    <p className="text-gray-600">No responses submitted.</p>
                  ) : (
                    responses.map((response, index) => (
                      <div
                        key={response.response_id}
                        className={`p-4 rounded-lg border mb-4 ${
                          response.option_id
                            ? "bg-gray-50 border-gray-200"
                            : "unanswered-question border-red-200"
                        }`}
                      >
                        <p className="text-gray-600">
                          <span className="font-semibold">
                            Question {index + 1} ID:
                          </span>{" "}
                          {response.question_id}
                        </p>
                        {response.option_id ? (
                          <>
                            <p className="text-gray-600">
                              <span className="font-semibold">Option ID:</span>{" "}
                              {response.option_id}
                            </p>
                            {response.value && (
                              <p className="text-gray-600">
                                <span className="font-semibold">Value:</span>{" "}
                                {response.value}
                              </p>
                            )}
                          </>
                        ) : (
                          <p className="missing-answer">
                            <span className="warning-icon">⚠️</span> No answer
                            provided
                          </p>
                        )}
                        <p className="text-gray-600">
                          <span className="font-semibold">Answered At:</span>{" "}
                          {new Date(response.answered_at).toLocaleString()}
                        </p>
                      </div>
                    ))
                  )}
                </div>
              )}
              <div className="mt-6 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
              </div>
            </div>
          </div>
        );
      };

      const SessionResultsModal = ({ sessionId, onClose, setNotification }) => {
        const [result, setResult] = React.useState(null);
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchResult = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/sessions/${sessionId}/profile-result`
              );
              setResult(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail ||
                  "Failed to load session results",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchResult();
        }, [sessionId]);

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment Results
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : !result ? (
                <p className="text-gray-600">No results available yet.</p>
              ) : (
                <div className="space-y-6">
                  <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                    <h3 className="text-2xl font-semibold text-blue-800 mb-2">
                      Your Personality Type: {result.profile.code} -{" "}
                      {result.profile.name}
                    </h3>
                    <p className="text-gray-700 mb-4">
                      {result.profile.description}
                    </p>
                    {result.confidence_pct && (
                      <div className="w-full bg-gray-200 rounded-full h-4">
                        <div
                          className="bg-blue-600 h-4 rounded-full"
                          style={{
                            width: `${(result.confidence_pct * 100).toFixed(
                              0
                            )}%`,
                          }}
                        ></div>
                      </div>
                    )}
                    <p className="text-sm text-gray-600 mt-2">
                      Confidence:{" "}
                      {result.confidence_pct
                        ? `${(result.confidence_pct * 100).toFixed(2)}%`
                        : "N/A"}
                    </p>
                  </div>

                  {result.profile.criteria_json && (
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <h4 className="text-lg font-medium text-gray-800 mb-3">
                        Detailed Analysis
                      </h4>
                      <div className="space-y-4">
                        {Object.entries(result.profile.criteria_json).map(
                          ([dimension, score]) => (
                            <div key={dimension} className="mb-3">
                              <div className="flex justify-between mb-1">
                                <span className="text-sm font-medium text-gray-700">
                                  {dimension}
                                </span>
                                <span className="text-sm font-medium text-gray-700">
                                  {typeof score === "number"
                                    ? `${(score * 100).toFixed(0)}%`
                                    : score}
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-indigo-600 h-2.5 rounded-full"
                                  style={{
                                    width:
                                      typeof score === "number"
                                        ? `${(score * 100).toFixed(0)}%`
                                        : "100%",
                                  }}
                                ></div>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="mt-6 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
              </div>
            </div>
          </div>
        );
      };

      const SessionResponseModal = ({
        sessionId,
        assessmentId,
        onClose,
        setNotification,
      }) => {
        const [questions, setQuestions] = React.useState([]);
        const [responses, setResponses] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchQuestions = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/${assessmentId}/questions`
              );
              setQuestions(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load questions",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchQuestions();
        }, [assessmentId]);

        const handleResponseChange = (questionId, optionId) => {
          setResponses({ ...responses, [questionId]: optionId });
        };

        const handleSubmit = async () => {
          const totalQuestions = questions.length;
          const answeredQuestions = Object.keys(responses).length;

          if (answeredQuestions !== totalQuestions) {
            const unansweredQuestions = questions.filter(
              (q) => !responses[q.question_id]
            );
            setNotification({
              message: `Please answer all questions before submitting. Missing answers for ${unansweredQuestions.length} questions.`,
              type: "error",
              details: unansweredQuestions.map((q) => q.body_md),
            });
            return;
          }

          setIsLoading(true);
          try {
            const responseData = questions.map((question) => {
              const selectedOption = question.options.find(
                (opt) => opt.option_id === responses[question.question_id]
              );
              return {
                question_id: question.question_id,
                option_id: responses[question.question_id],
                value: selectedOption ? selectedOption.value : null,
                answered_at: new Date().toISOString(),
              };
            });

            const response = await axios.post(
              `${API_BASE_URL}/sessions/${sessionId}/submit`,
              responseData
            );

            setNotification({
              message: "Responses submitted successfully! Results are ready.",
              type: "success",
            });
            onClose();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to submit responses",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment Questions
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : questions.length === 0 ? (
                <p className="text-gray-600">
                  No questions available for this assessment.
                </p>
              ) : (
                <div className="space-y-6">
                  {questions.map((question, index) => (
                    <div
                      key={question.question_id}
                      className={`bg-gray-50 p-4 rounded-lg border ${
                        responses[question.question_id]
                          ? "border-gray-200"
                          : "unanswered-question border-red-200"
                      }`}
                    >
                      <h3 className="text-lg font-medium text-gray-800 mb-3">
                        Question {index + 1}: {question.body_md}
                        {!responses[question.question_id] && (
                          <span className="missing-answer ml-2">
                            <span className="warning-icon">⚠️</span> Not
                            answered
                          </span>
                        )}
                      </h3>
                      <div className="space-y-2">
                        {question.options.map((option) => (
                          <label
                            key={option.option_id}
                            className="option-label flex items-center p-2 rounded-lg cursor-pointer"
                          >
                            <input
                              type="radio"
                              name={`question-${question.question_id}`}
                              value={option.option_id}
                              checked={
                                responses[question.question_id] ===
                                option.option_id
                              }
                              onChange={() =>
                                handleResponseChange(
                                  question.question_id,
                                  option.option_id
                                )
                              }
                              className="mr-2"
                            />
                            <span className="flex-1">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="spinner mr-2"></div>
                  ) : (
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 3"
                      />
                    </svg>
                  )}
                  Submit
                </button>
              </div>
            </div>
          </div>
        );
      };

      const StudentViewModal = ({
        assessmentId,
        assessmentName,
        onClose,
        setNotification,
      }) => {
        const [questions, setQuestions] = React.useState([]);
        const [responses, setResponses] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        React.useEffect(() => {
          const fetchQuestions = async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(
                `${API_BASE_URL}/${assessmentId}/questions`
              );
              setQuestions(response.data);
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to load questions",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          };
          fetchQuestions();
        }, [assessmentId]);

        const handleResponseChange = (questionId, optionId) => {
          setResponses({ ...responses, [questionId]: optionId });
        };

        const handleSubmit = () => {
          const totalQuestions = questions.length;
          const answeredQuestions = Object.keys(responses).length;

          if (answeredQuestions !== totalQuestions) {
            const unansweredQuestions = questions.filter(
              (q) => !responses[q.question_id]
            );
            setNotification({
              message: `Please answer all questions before submitting. Missing answers for ${unansweredQuestions.length} questions.`,
              type: "error",
              details: unansweredQuestions.map((q) => q.body_md),
            });
            return;
          }

          const responseDetails = questions.map((q) => ({
            question_id: q.question_id,
            option_id: responses[q.question_id],
            option_text:
              q.options.find((o) => o.option_id === responses[q.question_id])
                ?.label || "",
          }));
          setNotification({
            message: `Assessment preview submitted!`,
            details: responseDetails,
            type: "success",
          });
          onClose();
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Assessment: {assessmentName}
              </h2>
              {isLoading ? (
                <div className="flex justify-center">
                  <div className="spinner"></div>
                </div>
              ) : questions.length === 0 ? (
                <p className="text-gray-600">
                  No questions available for this assessment.
                </p>
              ) : (
                <div className="space-y-6">
                  {questions.map((question, index) => (
                    <div
                      key={question.question_id}
                      className={`bg-gray-50 p-4 rounded-lg border ${
                        responses[question.question_id]
                          ? "border-gray-200"
                          : "unanswered-question border-red-200"
                      }`}
                    >
                      <h3 className="text-lg font-medium text-gray-800 mb-3">
                        Question {index + 1}: {question.body_md}
                        {!responses[question.question_id] && (
                          <span className="missing-answer ml-2">
                            <span className="warning-icon">⚠️</span> Not
                            answered
                          </span>
                        )}
                      </h3>
                      <div className="space-y-2">
                        {question.options.map((option) => (
                          <label
                            key={option.option_id}
                            className="option-label flex items-center p-2 rounded-lg cursor-pointer"
                          >
                            <input
                              type="radio"
                              name={`question-${question.question_id}`}
                              value={option.option_id}
                              checked={
                                responses[question.question_id] ===
                                option.option_id
                              }
                              onChange={() =>
                                handleResponseChange(
                                  question.question_id,
                                  option.option_id
                                )
                              }
                              className="mr-2"
                            />
                            <span className="flex-1">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  Close
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 3"
                    />
                  </svg>
                  Submit
                </button>
              </div>
            </div>
          </div>
        );
      };

      const AssessmentForm = ({
        assessment,
        onSave,
        onCancel,
        setNotification,
      }) => {
        const [formData, setFormData] = React.useState(
          assessment || ASSESSMENT_TEMPLATE
        );
        const [errors, setErrors] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        // Track deletions for update flow
        const [removedDimensions, setRemovedDimensions] = React.useState([]);
        const [removedQuestions, setRemovedQuestions] = React.useState([]);
        const [removedOptions, setRemovedOptions] = React.useState([]);

        // ---------- Validation ----------
        const validateForm = () => {
          const newErrors = {};

          if (!formData.name) newErrors.name = "Name is required";
          if (!formData.code) newErrors.code = "Code is required";
          if (
            formData.version_int === undefined ||
            formData.version_int === null ||
            Number.isNaN(+formData.version_int) ||
            +formData.version_int < 0
          ) {
            newErrors.version_int = "Version must be non-negative";
          }

          formData.dimensions.forEach((dim, dIdx) => {
            if (!dim.name)
              newErrors[`dimension_name_${dIdx}`] =
                "Dimension name is required";
            if (!dim.code)
              newErrors[`dimension_code_${dIdx}`] =
                "Dimension code is required";
            if (
              dim.sequence === undefined ||
              dim.sequence === null ||
              +dim.sequence < 0
            ) {
              newErrors[`dimension_sequence_${dIdx}`] =
                "Sequence must be non-negative";
            }
          });

          const codes = new Set(
            formData.dimensions.map((d) => d.code).filter(Boolean)
          );

          formData.questions.forEach((que, qIdx) => {
            if (!que.body_md?.trim())
              newErrors[`question_body_${qIdx}`] = "Question body is required";
            if (!que.dimension_code) {
              newErrors[`question_dimension_${qIdx}`] =
                "Dimension code is required";
            } else if (!codes.has(que.dimension_code)) {
              newErrors[`question_dimension_${qIdx}`] =
                "Dimension code must match a listed dimension";
            }
            if (
              que.sequence === undefined ||
              que.sequence === null ||
              +que.sequence < 0
            ) {
              newErrors[`question_sequence_${qIdx}`] =
                "Sequence must be non-negative";
            }

            if (!que.options || que.options.length === 0) {
              newErrors[`question_options_${qIdx}`] =
                "At least one option is required";
            } else {
              que.options.forEach((opt, oIdx) => {
                if (!opt.label?.trim()) {
                  newErrors[`option_label_${qIdx}_${oIdx}`] =
                    "Option label is required";
                }
              });
            }
          });

          setErrors(newErrors);
          return Object.keys(newErrors).length === 0;
        };

        // ---------- Handlers ----------
        const handleChange = (e) => {
          const { name, value } = e.target;
          setFormData((prev) => ({
            ...prev,
            [name]:
              name === "version_int"
                ? value === ""
                  ? ""
                  : parseInt(value, 10)
                : value,
          }));
          setErrors((prev) => ({ ...prev, [name]: null }));
        };

        const handleDimensionChange = (index, field, value) => {
          setFormData((prev) => {
            const dims = prev.dimensions.map((d) => ({ ...d }));
            dims[index][field] =
              field === "sequence"
                ? value === ""
                  ? ""
                  : parseInt(value, 10)
                : value;
            return { ...prev, dimensions: dims };
          });
          setErrors((prev) => ({
            ...prev,
            [`dimension_${field}_${index}`]: null,
          }));
        };

        const handleQuestionChange = (index, field, value) => {
          setFormData((prev) => {
            const qs = prev.questions.map((q) => ({ ...q }));
            qs[index][field] =
              field === "sequence"
                ? value === ""
                  ? ""
                  : parseInt(value, 10)
                : value;
            return { ...prev, questions: qs };
          });
          setErrors((prev) => ({
            ...prev,
            [`question_${field}_${index}`]: null,
          }));
        };

        const handleOptionChange = (qIndex, oIndex, field, value) => {
          setFormData((prev) => {
            const qs = prev.questions.map((q) => ({
              ...q,
              options: q.options.map((o) => ({ ...o })),
            }));
            qs[qIndex].options[oIndex][field] = value;
            return { ...prev, questions: qs };
          });
          setErrors((prev) => ({
            ...prev,
            [`option_${field}_${qIndex}_${oIndex}`]: null,
          }));
        };

        const addDimension = () => {
          setFormData((prev) => ({
            ...prev,
            dimensions: [
              ...prev.dimensions,
              {
                dimension_id: null,
                name: "",
                code: "",
                sequence: prev.dimensions.length,
              },
            ],
          }));
        };

        const removeDimension = (index) => {
          setFormData((prev) => {
            const dims = prev.dimensions.map((d) => ({ ...d }));
            const toRemove = dims[index];
            if (toRemove.dimension_id) {
              setRemovedDimensions((rd) => [...rd, toRemove.dimension_id]);
            }
            dims.splice(index, 1);
            return { ...prev, dimensions: dims };
          });
        };

        const addQuestion = () => {
          setFormData((prev) => ({
            ...prev,
            questions: [
              ...prev.questions,
              {
                question_id: null,
                body_md: "",
                dimension_code: "",
                sequence: prev.questions.length,
                block_label: "",
                options: [
                  {
                    option_id: null,
                    label: "",
                    value: null,
                    is_correct: false,
                  },
                ],
              },
            ],
          }));
        };

        const removeQuestion = (index) => {
          setFormData((prev) => {
            const qs = prev.questions.map((q) => ({ ...q }));
            const q = qs[index];
            if (q.question_id) {
              setRemovedQuestions((rq) => [...rq, q.question_id]);
              const optIds = (q.options || [])
                .filter((o) => o.option_id)
                .map((o) => o.option_id);
              if (optIds.length) setRemovedOptions((ro) => [...ro, ...optIds]);
            }
            qs.splice(index, 1);
            return { ...prev, questions: qs };
          });
        };

        const addOption = (qIndex) => {
          setFormData((prev) => {
            const qs = prev.questions.map((q) => ({
              ...q,
              options: q.options.map((o) => ({ ...o })),
            }));
            qs[qIndex].options.push({
              option_id: null,
              label: "",
              value: null,
              is_correct: false,
            });
            return { ...prev, questions: qs };
          });
        };

        const removeOption = (qIndex, oIndex) => {
          setFormData((prev) => {
            const qs = prev.questions.map((q) => ({
              ...q,
              options: q.options.map((o) => ({ ...o })),
            }));
            const opt = qs[qIndex].options[oIndex];
            if (opt.option_id)
              setRemovedOptions((ro) => [...ro, opt.option_id]);
            qs[qIndex].options.splice(oIndex, 1);
            return { ...prev, questions: qs };
          });
        };

        // ---------- Submit ----------
        const handleSubmit = async (e) => {
          e.preventDefault();
          if (!validateForm()) return;

          setIsLoading(true);
          try {
            // Base assessment payload for create/update
            const basePayload = {
              name: formData.name,
              code: formData.code, // "MOCK" | "REAL" | "QUIZ"
              version_int: parseInt(formData.version_int, 10),
            };

            if (assessment?.assessment_id) {
              // ===== UPDATE ASSESSMENT (flat API) =====
              const aid = assessment.assessment_id;

              // Update assessment meta
              await axios.put(
                `${API_BASE_URL}/assessments/${aid}`,
                basePayload
              );

              // Delete removed entities
              for (const dimensionId of removedDimensions) {
                await axios.delete(`${API_BASE_URL}/dimensions/${dimensionId}`);
              }
              for (const questionId of removedQuestions) {
                await axios.delete(`${API_BASE_URL}/questions/${questionId}`);
              }
              for (const optionId of removedOptions) {
                await axios.delete(`${API_BASE_URL}/options/${optionId}`);
              }

              // Upsert dimensions
              // (We’ll also build a map code->id for questions)
              const codeToId = {};
              for (const dim of formData.dimensions) {
                if (dim.dimension_id) {
                  await axios.put(
                    `${API_BASE_URL}/dimensions/${dim.dimension_id}`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                  codeToId[dim.code] = dim.dimension_id;
                } else {
                  const d = await axios.post(
                    `${API_BASE_URL}/assessments/${aid}/dimensions`,
                    {
                      name: dim.name,
                      code: dim.code,
                      sequence: dim.sequence,
                    }
                  );
                  codeToId[dim.code] = d.data.dimension_id;
                }
              }

              // Upsert questions & options
              for (const q of formData.questions) {
                const dimension_id = codeToId[q.dimension_code];
                if (q.question_id) {
                  await axios.put(
                    `${API_BASE_URL}/questions/${q.question_id}`,
                    {
                      body_md: q.body_md,
                      dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );

                  for (const opt of q.options) {
                    if (opt.option_id) {
                      await axios.put(
                        `${API_BASE_URL}/options/${opt.option_id}`,
                        {
                          label: opt.label,
                          value:
                            opt.value === "" ||
                            opt.value === null ||
                            Number.isNaN(+opt.value)
                              ? null
                              : parseInt(opt.value, 10),
                          is_correct: !!opt.is_correct,
                        }
                      );
                    } else {
                      const o = await axios.post(
                        `${API_BASE_URL}/questions/${q.question_id}/options`,
                        {
                          label: opt.label,
                          value:
                            opt.value === "" ||
                            opt.value === null ||
                            Number.isNaN(+opt.value)
                              ? null
                              : parseInt(opt.value, 10),
                          is_correct: !!opt.is_correct,
                        }
                      );
                      opt.option_id = o.data.option_id;
                    }
                  }
                } else {
                  const qRes = await axios.post(
                    `${API_BASE_URL}/assessments/${aid}/questions`,
                    {
                      body_md: q.body_md,
                      dimension_id,
                      sequence: q.sequence,
                      block_label: q.block_label || null,
                    }
                  );
                  const newQid = qRes.data.question_id;

                  for (const opt of q.options) {
                    const o = await axios.post(
                      `${API_BASE_URL}/questions/${newQid}/options`,
                      {
                        label: opt.label,
                        value:
                          opt.value === "" ||
                          opt.value === null ||
                          Number.isNaN(+opt.value)
                            ? null
                            : parseInt(opt.value, 10),
                        is_correct: !!opt.is_correct,
                      }
                    );
                    opt.option_id = o.data.option_id;
                  }
                }
              }

              setNotification?.({
                message: "Assessment updated successfully!",
                type: "success",
              });
              onSave?.("Assessment updated successfully!", "success");
            } else {
              // ===== CREATE ASSESSMENT (nested) =====
              const payload = {
                ...basePayload,
                dimensions: formData.dimensions.map((d, idx) => ({
                  name: d.name,
                  code: d.code,
                  sequence: Number.isFinite(+d.sequence) ? +d.sequence : idx,
                  value: d.value ?? null,
                })),
                questions: formData.questions.map((q, qIdx) => ({
                  body_md: q.body_md,
                  dimension_code: q.dimension_code,
                  sequence: Number.isFinite(+q.sequence) ? +q.sequence : qIdx,
                  block_label: q.block_label || null,
                  options: (q.options || []).map((o) => ({
                    label: o.label,
                    value:
                      o.value === "" ||
                      o.value === null ||
                      Number.isNaN(+o.value)
                        ? null
                        : parseInt(o.value, 10),
                    is_correct: !!o.is_correct,
                  })),
                })),
              };

              await axios.post(`${API_BASE_URL}/assessments`, payload);

              setNotification?.({
                message: "Assessment created successfully!",
                type: "success",
              });
              onSave?.("Assessment created successfully!", "success");
            }
          } catch (error) {
            console.error("Error saving assessment:", error);
            const msg =
              error?.response?.data?.detail || "Failed to save assessment";
            setErrors((prev) => ({ ...prev, submit: msg }));
            setNotification?.({ message: msg, type: "error" });
          } finally {
            setIsLoading(false);
          }
        };

        // ---------- UI ----------
        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                {assessment ? "Edit Assessment" : "Create Assessment"}
              </h2>

              {errors.submit && (
                <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                  {errors.submit}
                </div>
              )}

              <form onSubmit={handleSubmit}>
                {/* Name */}
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.name ? "border-red-500" : "border-gray-300"
                    }`}
                    disabled={isLoading}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                  )}
                </div>

                {/* Code */}
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Code
                  </label>
                  <select
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.code ? "border-red-500" : "border-gray-300"
                    }`}
                    disabled={isLoading}
                  >
                    <option value="">Select Code</option>
                    <option value="MOCK">Mock</option>
                    <option value="REAL">Real</option>
                    <option value="QUIZ">Quiz</option>
                  </select>
                  {errors.code && (
                    <p className="text-red-500 text-sm mt-1">{errors.code}</p>
                  )}
                </div>

                {/* Version */}
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Version
                  </label>
                  <input
                    type="number"
                    name="version_int"
                    value={formData.version_int}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.version_int ? "border-red-500" : "border-gray-300"
                    }`}
                    disabled={isLoading}
                  />
                  {errors.version_int && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.version_int}
                    </p>
                  )}
                </div>

                {/* Dimensions */}
                <h3 className="text-xl font-semibold mb-4 text-gray-800">
                  Dimensions
                </h3>
                {formData.dimensions.map((dim, index) => (
                  <div
                    key={dim.dimension_id ?? `dim-${index}`}
                    className="mb-5 p-5 bg-gray-50 rounded-lg border border-gray-200 relative"
                  >
                    <button
                      type="button"
                      onClick={() => removeDimension(index)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                      disabled={isLoading}
                      aria-label="Remove dimension"
                      title="Remove dimension"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>

                    <select
                      value={dim.name}
                      onChange={(e) =>
                        handleDimensionChange(index, "name", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_name_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Type</option>
                      {DIMENSION_TYPES.map((type) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                    {errors[`dimension_name_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_name_${index}`]}
                      </p>
                    )}

                    <select
                      value={dim.code}
                      onChange={(e) =>
                        handleDimensionChange(index, "code", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_code_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Code</option>
                      {DIMENSION_CODES.map((code) => (
                        <option key={code} value={code}>
                          {code}
                        </option>
                      ))}
                    </select>
                    {errors[`dimension_code_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_code_${index}`]}
                      </p>
                    )}

                    <input
                      type="number"
                      placeholder="Sequence"
                      value={dim.sequence}
                      onChange={(e) =>
                        handleDimensionChange(index, "sequence", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                        errors[`dimension_sequence_${index}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`dimension_sequence_${index}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`dimension_sequence_${index}`]}
                      </p>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addDimension}
                  className="mb-5 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  disabled={isLoading}
                >
                  Add Dimension
                </button>

                {/* Questions */}
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  Questions
                </h3>
                {formData.questions.map((que, qIndex) => (
                  <div
                    key={que.question_id ?? `q-${qIndex}`}
                    className="mb-5 p-5 bg-gray-50 rounded-lg border border-gray-200 relative"
                  >
                    <button
                      type="button"
                      onClick={() => removeQuestion(qIndex)}
                      className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                      disabled={isLoading}
                      aria-label="Remove question"
                      title="Remove question"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>

                    <input
                      type="text"
                      placeholder="Question Body (Markdown)"
                      value={que.body_md}
                      onChange={(e) =>
                        handleQuestionChange(qIndex, "body_md", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_body_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`question_body_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_body_${qIndex}`]}
                      </p>
                    )}

                    <select
                      value={que.dimension_code}
                      onChange={(e) =>
                        handleQuestionChange(
                          qIndex,
                          "dimension_code",
                          e.target.value
                        )
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_dimension_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">Select Dimension Code</option>
                      {formData.dimensions.map((dim) => (
                        <option key={`${dim.code}-${qIndex}`} value={dim.code}>
                          {dim.code}
                        </option>
                      ))}
                    </select>
                    {errors[`question_dimension_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_dimension_${qIndex}`]}
                      </p>
                    )}

                    <input
                      type="number"
                      placeholder="Sequence"
                      value={que.sequence}
                      onChange={(e) =>
                        handleQuestionChange(qIndex, "sequence", e.target.value)
                      }
                      className={`w-full p-3 border rounded-lg mb-2 focus:ring-2 focus:ring-blue-500 ${
                        errors[`question_sequence_${qIndex}`]
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    />
                    {errors[`question_sequence_${qIndex}`] && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors[`question_sequence_${qIndex}`]}
                      </p>
                    )}

                    <input
                      type="text"
                      placeholder="Block Label (Optional)"
                      value={que.block_label || ""}
                      onChange={(e) =>
                        handleQuestionChange(
                          qIndex,
                          "block_label",
                          e.target.value
                        )
                      }
                      className="w-full p-3 border rounded-lg mb-4 focus:ring-2 focus:ring-blue-500 border-gray-300"
                      disabled={isLoading}
                    />

                    <h4 className="text-md font-medium mb-2 text-gray-700">
                      Options
                    </h4>
                    {errors[`question_options_${qIndex}`] && (
                      <p className="text-red-500 text-sm mb-2">
                        {errors[`question_options_${qIndex}`]}
                      </p>
                    )}

                    {que.options.map((opt, oIndex) => (
                      <div
                        key={opt.option_id ?? `opt-${qIndex}-${oIndex}`}
                        className="flex space-x-2 items-center mb-2"
                      >
                        <div className="flex-1">
                          <input
                            type="text"
                            placeholder="Option Label"
                            value={opt.label}
                            onChange={(e) =>
                              handleOptionChange(
                                qIndex,
                                oIndex,
                                "label",
                                e.target.value
                              )
                            }
                            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                              errors[`option_label_${qIndex}_${oIndex}`]
                                ? "border-red-500"
                                : "border-gray-300"
                            }`}
                            disabled={isLoading}
                          />
                          {errors[`option_label_${qIndex}_${oIndex}`] && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors[`option_label_${qIndex}_${oIndex}`]}
                            </p>
                          )}
                        </div>

                        <input
                          type="number"
                          placeholder="Value (integer)"
                          value={opt.value ?? ""}
                          onChange={(e) =>
                            handleOptionChange(
                              qIndex,
                              oIndex,
                              "value",
                              e.target.value === "" ? null : parseInt(e.target.value, 10)
                            )
                          }
                          className="w-36 p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 border-gray-300"
                          disabled={isLoading}
                        />

                        <label className="flex items-center gap-2 px-3 py-2 border rounded-lg">
                          <input
                            type="checkbox"
                            checked={!!opt.is_correct}
                            onChange={(e) =>
                              handleOptionChange(
                                qIndex,
                                oIndex,
                                "is_correct",
                                e.target.checked
                              )
                            }
                            disabled={isLoading}
                          />
                          <span>Correct</span>
                        </label>

                        <button
                          type="button"
                          onClick={() => removeOption(qIndex, oIndex)}
                          className="text-red-600 hover:text-red-800"
                          disabled={isLoading}
                          aria-label="Remove option"
                          title="Remove option"
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}

                    <button
                      type="button"
                      onClick={() => addOption(qIndex)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center mt-2"
                      disabled={isLoading}
                    >
                      Add Option
                    </button>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addQuestion}
                  className="mb-5 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  disabled={isLoading}
                >
                  Add Question
                </button>

                {/* Actions */}
                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center ${
                      isLoading ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    {isLoading ? <div className="spinner mr-2"></div> : null}
                    Save
                  </button>
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
                    disabled={isLoading}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      };

      const LessonForm = ({ lesson, courseId, onSave, onCancel }) => {
        const [formData, setFormData] = React.useState(
          lesson || LESSON_TEMPLATE
        );
        const [errors, setErrors] = React.useState({});
        const [isLoading, setIsLoading] = React.useState(false);

        const validateForm = () => {
          const newErrors = {};
          if (!formData.title) newErrors.title = "Lesson title is required";
          setErrors(newErrors);
          return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
          e.preventDefault();
          if (!validateForm()) return;

          setIsLoading(true);
          try {
            const lessonPayload = {
              title: formData.title,
              body_md: formData.body_md || "",
              video_url: formData.video_url || null,
              file_url: formData.file_url || null,
              content_type: formData.content_type || "TEXT",
            };

            if (lesson?.lesson_id) {
              // Update existing lesson
              await axios.put(
                `${API_BASE_URL}/courses/lessons/${lesson.lesson_id}`,
                lessonPayload
              );
              onSave("Lesson updated successfully!", "success");
            } else {
              // Create new lesson
              await axios.post(
                `${API_BASE_URL}/courses/${courseId}/lessons`,
                lessonPayload
              );
              onSave("Lesson created successfully!", "success");
            }
          } catch (error) {
            console.error("Error saving lesson:", error);
            onSave(
              error.response?.data?.detail || "Failed to save lesson",
              "error"
            );
          } finally {
            setIsLoading(false);
          }
        };

        const handleChange = (e) => {
          const { name, value } = e.target;
          setFormData({ ...formData, [name]: value });
          setErrors({ ...errors, [name]: null });
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                {lesson ? "Edit Lesson" : "Create New Lesson"}
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 ${
                      errors.title ? "border-red-500" : "border-gray-300"
                    }`}
                    disabled={isLoading}
                  />
                  {errors.title && (
                    <p className="text-red-500 text-sm mt-1">{errors.title}</p>
                  )}
                </div>

                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Content Type
                  </label>
                  <select
                    name="content_type"
                    value={formData.content_type}
                    onChange={handleChange}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    disabled={isLoading}
                  >
                    {LESSON_CONTENT_TYPES.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-5">
                  <label className="block text-gray-700 font-medium mb-1">
                    Content (Markdown supported)
                  </label>
                  <textarea
                    name="body_md"
                    value={formData.body_md}
                    onChange={handleChange}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                    rows="6"
                    disabled={isLoading}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                  <div>
                    <label className="block text-gray-700 font-medium mb-1">
                      Video URL (optional)
                    </label>
                    <input
                      type="url"
                      name="video_url"
                      value={formData.video_url}
                      onChange={handleChange}
                      className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 font-medium mb-1">
                      File URL (optional)
                    </label>
                    <input
                      type="url"
                      name="file_url"
                      value={formData.file_url}
                      onChange={handleChange}
                      className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 border-gray-300"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
                    disabled={isLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                    disabled={isLoading}
                  >
                    {isLoading && <div className="spinner mr-2"></div>}
                    {lesson ? "Update Lesson" : "Create Lesson"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      };

      const CourseContentManager = ({
        course,
        onClose,
        onSave,
        assessments,
      }) => {
        const [lessons, setLessons] = React.useState([]);
        const [contents, setContents] = React.useState([]);
        const [isLoading, setIsLoading] = React.useState(false);
        const [showAddContent, setShowAddContent] = React.useState(false);
        const [newContent, setNewContent] = React.useState({
          content_type: "text",
          sequence: 0,
          lesson_id: null,
          assessment_id: null,
        });

        React.useEffect(() => {
          if (course?.course_id) {
            fetchCourseLessons();
            fetchCourseContents();
          }
        }, [course]);

        const fetchCourseLessons = async () => {
          try {
            const response = await axios.get(
              `${API_BASE_URL}/courses/${course.course_id}/lessons`
            );
            setLessons(response.data);
          } catch (error) {
            console.error("Failed to fetch lessons:", error);
          }
        };

        const fetchCourseContents = async () => {
          try {
            const response = await axios.get(
              `${API_BASE_URL}/courses/${course.course_id}`
            );
            setContents(response.data.contents || []);
          } catch (error) {
            console.error("Failed to fetch course contents:", error);
          }
        };

        const handleAddContent = async () => {
          if (!newContent.lesson_id && !newContent.assessment_id) {
            alert("Please select either a lesson or an assessment");
            return;
          }

          setIsLoading(true);
          try {
            await axios.post(
              `${API_BASE_URL}/courses/${course.course_id}/contents`,
              {
                content_type: newContent.content_type,
                sequence: newContent.sequence,
                lesson_id: newContent.lesson_id || null,
                assessment_id: newContent.assessment_id || null,
              }
            );

            setNewContent({
              content_type: "text",
              sequence: contents.length,
              lesson_id: null,
              assessment_id: null,
            });
            setShowAddContent(false);
            fetchCourseContents();
            onSave("Content added successfully!", "success");
          } catch (error) {
            console.error("Failed to add content:", error);
            onSave(
              error.response?.data?.detail || "Failed to add content",
              "error"
            );
          } finally {
            setIsLoading(false);
          }
        };

        const handleDeleteContent = async (contentId) => {
          if (!confirm("Are you sure you want to remove this content?")) return;

          setIsLoading(true);
          try {
            await axios.delete(`${API_BASE_URL}/courses/contents/${contentId}`);
            fetchCourseContents();
            onSave("Content removed successfully!", "success");
          } catch (error) {
            console.error("Failed to delete content:", error);
            onSave(
              error.response?.data?.detail || "Failed to remove content",
              "error"
            );
          } finally {
            setIsLoading(false);
          }
        };

        return (
          <div className="modal-overlay fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                Manage Course Content: {course.title}
              </h2>

              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-semibold text-gray-800">
                    Course Contents
                  </h3>
                  <button
                    onClick={() => setShowAddContent(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                  >
                    Add Content
                  </button>
                </div>

                {contents.length === 0 ? (
                  <p className="text-gray-500 italic">No content added yet.</p>
                ) : (
                  <div className="space-y-3">
                    {contents
                      .sort((a, b) => a.sequence - b.sequence)
                      .map((content, index) => (
                        <div
                          key={content.content_id}
                          className="p-4 bg-gray-50 rounded-lg border border-gray-200 flex justify-between items-center"
                        >
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium">
                                {content.sequence + 1}
                              </span>
                              <span className="font-medium capitalize">
                                {content.content_type}
                              </span>
                            </div>
                            {content.lesson && (
                              <p className="text-sm text-gray-600 mt-1 ml-10">
                                Lesson: {content.lesson.title}
                              </p>
                            )}
                            {content.assessment && (
                              <p className="text-sm text-gray-600 mt-1 ml-10">
                                Assessment: {content.assessment.name}
                              </p>
                            )}
                          </div>
                          <button
                            onClick={() =>
                              handleDeleteContent(content.content_id)
                            }
                            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition text-sm"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                  </div>
                )}
              </div>

              {showAddContent && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="text-lg font-medium text-blue-800 mb-3">
                    Add New Content
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Content Type
                      </label>
                      <select
                        value={newContent.content_type}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            content_type: e.target.value,
                          })
                        }
                        className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                      >
                        {CONTENT_TYPES.map((type) => (
                          <option key={type} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sequence
                      </label>
                      <input
                        type="number"
                        value={newContent.sequence}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            sequence: parseInt(e.target.value),
                          })
                        }
                        className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                        min="0"
                      />
                    </div>
                  </div>

                  {newContent.content_type === "text" && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Select Lesson
                      </label>
                      <select
                        value={newContent.lesson_id || ""}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            lesson_id: e.target.value || null,
                            assessment_id: null,
                          })
                        }
                        className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select a lesson</option>
                        {lessons.map((lesson) => (
                          <option
                            key={lesson.lesson_id}
                            value={lesson.lesson_id}
                          >
                            {lesson.title}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {newContent.content_type === "assessment" && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Select Assessment
                      </label>
                      <select
                        value={newContent.assessment_id || ""}
                        onChange={(e) =>
                          setNewContent({
                            ...newContent,
                            assessment_id: e.target.value || null,
                            lesson_id: null,
                          })
                        }
                        className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select an assessment</option>
                        {assessments.map((assessment) => (
                          <option
                            key={assessment.assessment_id}
                            value={assessment.assessment_id}
                          >
                            {assessment.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <button
                      onClick={handleAddContent}
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
                      disabled={isLoading}
                    >
                      {isLoading ? "Adding..." : "Add Content"}
                    </button>
                    <button
                      onClick={() => setShowAddContent(false)}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        );
      };

      const App = () => {
        const [assessments, setAssessments] = React.useState([]);
        const [sessions, setSessions] = React.useState([]);
        const [courses, setCourses] = React.useState([]);
        const [selectedAssessment, setSelectedAssessment] =
          React.useState(null);
        const [selectedCourse, setSelectedCourse] = React.useState(null);
        const [selectedLesson, setSelectedLesson] = React.useState(null);
        const [showForm, setShowForm] = React.useState(false);
        const [showCourseForm, setShowCourseForm] = React.useState(false);
        const [showLessonForm, setShowLessonForm] = React.useState(false);
        const [showContentManager, setShowContentManager] =
          React.useState(false);
        const [showStudentView, setShowStudentView] = React.useState(null);
        const [showSessionView, setShowSessionView] = React.useState(null);
        const [showSessionResults, setShowSessionResults] =
          React.useState(null);
        const [showSessionResponse, setShowSessionResponse] =
          React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [isLoading, setIsLoading] = React.useState(false);
        const [isAuthenticated, setIsAuthenticated] = React.useState(false);
        const [activeTab, setActiveTab] = React.useState("courses");

        React.useEffect(() => {
          const token = localStorage.getItem("access_token");
          if (token) {
            setIsAuthenticated(true);
            fetchAssessments();
            fetchSessions();
            fetchCourses();
          }
        }, []);

        const fetchAssessments = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/assessments`);
            setAssessments(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch assessments",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const fetchSessions = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/sessions`);
            setSessions(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch sessions",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const fetchCourses = async () => {
          setIsLoading(true);
          try {
            const response = await axios.get(`${API_BASE_URL}/courses`);
            setCourses(response.data);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to fetch courses",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleLogin = () => {
          setIsAuthenticated(true);
          fetchAssessments();
          fetchSessions();
          fetchCourses();
        };

        const handleLogout = () => {
          localStorage.removeItem("access_token");
          setIsAuthenticated(false);
          setAssessments([]);
          setSessions([]);
          setSelectedAssessment(null);
          setShowForm(false);
          setShowStudentView(null);
          setShowSessionView(null);
          setShowSessionResults(null);
          setShowSessionResponse(null);
          setNotification({
            message: "Logged out successfully",
            type: "success",
          });
        };

        const handleEdit = async (assessment) => {
          try {
            const [dimensionsResponse, questionsResponse] = await Promise.all([
              axios.get(
                `${API_BASE_URL}/${assessment.assessment_id}/dimensions`
              ),
              axios.get(
                `${API_BASE_URL}/${assessment.assessment_id}/questions`
              ),
            ]);
            setSelectedAssessment({
              ...assessment,
              dimensions: dimensionsResponse.data,
              questions: questionsResponse.data.map((q) => ({
                ...q,
                dimension_code: q.dimension.code,
                options: q.options,
              })),
            });
            setShowForm(true);
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail ||
                "Failed to load assessment details",
              type: "error",
            });
          }
        };

        const handleDelete = async (assessmentId) => {
          if (
            window.confirm("Are you sure you want to delete this assessment?")
          ) {
            setIsLoading(true);
            try {
              await axios.delete(`${API_BASE_URL}/assessments/${assessmentId}`);
              setNotification({
                message: "Assessment deleted successfully",
                type: "success",
              });
              await fetchAssessments();
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to delete assessment",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          }
        };

        const handleStartSession = async (assessmentId, assessmentName) => {
          setIsLoading(true);
          try {
            const response = await axios.post(`${API_BASE_URL}/sessions`, {
              assessment_id: assessmentId,
            });
            setNotification({
              message: `Session created for ${assessmentName}`,
              type: "success",
            });
            await fetchSessions();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to create session",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleViewAsStudent = (assessmentId, assessmentName) => {
          setShowStudentView({ assessmentId, assessmentName });
        };

        const handleSessionView = (sessionId) => {
          setShowSessionView(sessionId);
        };

        const handleSessionResults = (sessionId) => {
          setShowSessionResults(sessionId);
        };

        const handleSubmitResponses = (sessionId, assessmentId) => {
          setShowSessionResponse({ sessionId, assessmentId });
        };

        const handleDeleteSession = async (sessionId) => {
          if (window.confirm("Are you sure you want to delete this session?")) {
            setIsLoading(true);
            try {
              await axios.delete(`${API_BASE_URL}/sessions/${sessionId}`);
              setNotification({
                message: "Session deleted successfully",
                type: "success",
              });
              await fetchSessions();
            } catch (error) {
              setNotification({
                message:
                  error.response?.data?.detail || "Failed to delete session",
                type: "error",
              });
            } finally {
              setIsLoading(false);
            }
          }
        };

        const handleSave = (message, type) => {
          setShowForm(false);
          setSelectedAssessment(null);
          setNotification({ message, type });
          fetchAssessments();
        };

        const handleCourseSave = (savedCourse) => {
          setShowCourseForm(false);
          setSelectedCourse(null);
          setNotification({
            message: "Course saved successfully!",
            type: "success",
          });
          fetchCourses();
        };

        const handleEditCourse = (course) => {
          setSelectedCourse(course);
          setShowCourseForm(true);
        };

        const handleManageLessons = (course) => {
          setSelectedCourse(course);
          setShowLessonForm(true);
        };

        const handleManageContent = (course) => {
          setSelectedCourse(course);
          setShowContentManager(true);
        };

        const handleDeleteCourse = async (courseId) => {
          if (!confirm("Are you sure you want to delete this course?")) return;

          setIsLoading(true);
          try {
            await axios.delete(`${API_BASE_URL}/courses/${courseId}`);
            setNotification({
              message: "Course deleted successfully!",
              type: "success",
            });
            fetchCourses();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to delete course",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleLessonSave = (message, type) => {
          setShowLessonForm(false);
          setSelectedLesson(null);
          setNotification({ message, type });
          fetchCourses(); // Refresh courses to show updated lessons
        };

        const handleEditLesson = (lesson) => {
          setSelectedLesson(lesson);
          setShowLessonForm(true);
        };

        const handleDeleteLesson = async (lessonId) => {
          if (!confirm("Are you sure you want to delete this lesson?")) return;

          setIsLoading(true);
          try {
            await axios.delete(`${API_BASE_URL}/courses/lessons/${lessonId}`);
            setNotification({
              message: "Lesson deleted successfully!",
              type: "success",
            });
            fetchCourses();
          } catch (error) {
            setNotification({
              message:
                error.response?.data?.detail || "Failed to delete lesson",
              type: "error",
            });
          } finally {
            setIsLoading(false);
          }
        };

        const handleCancel = () => {
          setShowForm(false);
          setSelectedAssessment(null);
        };

        const handleCancelCourse = () => {
          setShowCourseForm(false);
          setSelectedCourse(null);
        };

        const handleCancelLesson = () => {
          setShowLessonForm(false);
          setSelectedLesson(null);
        };

        const handleContentManagerSave = (message, type) => {
          setNotification({ message, type });
          fetchCourses(); // Refresh courses to show updated content
        };

        const handleCloseContentManager = () => {
          setShowContentManager(false);
          setSelectedCourse(null);
        };

        const closeNotification = () => {
          setNotification(null);
        };

        if (!isAuthenticated) {
          return <LoginModal onLogin={handleLogin} />;
        }

        return (
          <div className="container mx-auto p-6 max-w-7xl">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-4xl font-bold text-gray-800 flex items-center">
                <svg
                  className="w-8 h-8 mr-3 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Course & Assessment Manager
              </h1>
              <button
                onClick={handleLogout}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                Logout
              </button>
            </div>
            <div className="flex mb-6 border-b border-gray-200">
              <button
                onClick={() => setActiveTab("assessments")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "assessments" ? "tab-active" : ""
                }`}
              >
                Assessments
              </button>
              <button
                onClick={() => setActiveTab("sessions")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "sessions" ? "tab-active" : ""
                }`}
              >
                Sessions
              </button>
              <button
                onClick={() => setActiveTab("courses")}
                className={`px-4 py-2 text-gray-600 hover:text-gray-800 ${
                  activeTab === "courses" ? "tab-active" : ""
                }`}
              >
                Courses
              </button>
            </div>
            {activeTab === "assessments" && (
              <>
                <button
                  onClick={() => setShowForm(true)}
                  className="mb-6 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors hover-scale flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create New Assessment
                </button>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {assessments.map((assessment) => (
                    <AssessmentCard
                      key={assessment.assessment_id}
                      assessment={assessment}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onViewAsStudent={handleViewAsStudent}
                      onStartSession={handleStartSession}
                    />
                  ))}
                </div>
              </>
            )}
            {activeTab === "sessions" && (
              <>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {sessions.map((session) => (
                    <SessionCard
                      key={session.session_id}
                      session={session}
                      onView={handleSessionView}
                      onDelete={handleDeleteSession}
                      onSubmitResponses={handleSubmitResponses}
                      onViewResults={handleSessionResults}
                    />
                  ))}
                </div>
              </>
            )}
            {activeTab === "courses" && (
              <>
                <button
                  onClick={() => setShowCourseForm(true)}
                  className="mb-6 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors hover-scale flex items-center"
                  disabled={isLoading}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Create New Course
                </button>
                {isLoading && (
                  <div className="flex justify-center mb-6">
                    <div className="spinner"></div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {courses.map((course) => (
                    <CourseCard
                      key={course.course_id}
                      course={course}
                      onEdit={handleEditCourse}
                      onDelete={handleDeleteCourse}
                      onManageLessons={handleManageLessons}
                      onManageContent={handleManageContent}
                    />
                  ))}
                </div>
              </>
            )}
            {notification && (
              <Notification
                message={notification.message}
                type={notification.type}
                onClose={closeNotification}
                details={notification.details}
              />
            )}
            {showForm && (
              <AssessmentForm
                assessment={selectedAssessment}
                onSave={handleSave}
                onCancel={handleCancel}
                setNotification={setNotification}
              />
            )}
            {showCourseForm && (
              <CourseForm
                course={selectedCourse}
                onSave={handleCourseSave}
                onCancel={handleCancelCourse}
                assessments={assessments}
              />
            )}
            {showLessonForm && (
              <LessonForm
                lesson={selectedLesson}
                courseId={selectedCourse?.course_id}
                onSave={handleLessonSave}
                onCancel={handleCancelLesson}
              />
            )}
            {showContentManager && (
              <CourseContentManager
                course={selectedCourse}
                onClose={handleCloseContentManager}
                onSave={handleContentManagerSave}
                assessments={assessments}
              />
            )}
            {showStudentView && (
              <StudentViewModal
                assessmentId={showStudentView.assessmentId}
                assessmentName={showStudentView.assessmentName}
                onClose={() => setShowStudentView(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionView && (
              <SessionViewModal
                sessionId={showSessionView}
                onClose={() => setShowSessionView(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionResults && (
              <SessionResultsModal
                sessionId={showSessionResults}
                onClose={() => setShowSessionResults(null)}
                setNotification={setNotification}
              />
            )}
            {showSessionResponse && (
              <SessionResponseModal
                sessionId={showSessionResponse.sessionId}
                assessmentId={showSessionResponse.assessmentId}
                onClose={() => setShowSessionResponse(null)}
                setNotification={setNotification}
              />
            )}
          </div>
        );
      };

      const root = ReactDOM.createRoot(document.getElementById("root"));
      root.render(<App />);
    </script>
  </body>
</html>
