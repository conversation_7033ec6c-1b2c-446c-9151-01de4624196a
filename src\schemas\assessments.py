from enum import EnumType

from pydantic import BaseModel, ConfigDict, EmailStr, HttpUrl, Field, constr
from typing import Optional, List
from datetime import datetime
from uuid import UUID

from src.DB.enums import AssessmentType, ScoringMethod, DimensionType, DimensionCode
from src.DB.enums import UserGender, UserRole

class ResultProfileCreate(BaseModel):
    code: str
    name: str
    description: str
    criteria_json: Optional[dict] = None

class ResultProfileCreateList(BaseModel):
    assessment_id: UUID
    profiles_data: List[ResultProfileCreate]

    model_config = ConfigDict(from_attributes=True)


class ResultProfileRead(BaseModel):
    profile_id: UUID
    assessment_id: UUID
    code: str
    name: str
    description: str
    criteria_json: Optional[dict]
    model_config = ConfigDict(from_attributes=True)

class ResultProfileReadSim(BaseModel):
    profile_id: UUID
    assessment_id: UUID
    code: str
    name: str
    description: str
    model_config = ConfigDict(from_attributes=True)

class ResultProfileUpdate(BaseModel):
    code: Optional[str] = None
    assessment_id: Optional[UUID] = None
    name: Optional[str] = None
    description: Optional[str] = None
    criteria_json: Optional[dict] = None

class OptionCreate(BaseModel):
    label: str = Field(..., min_length=1)
    value: Optional[int] = None
    is_correct: Optional[bool] = False

class OptionUpdate(BaseModel):
    label: Optional[str] = Field(None, min_length=1)
    value: Optional[int] = None

class QuestionCreate(BaseModel):
    body_md: str = Field(..., min_length=1)
    dimension_code: DimensionCode = Field(...)
    sequence: int = Field(..., ge=0)
    block_label: Optional[str] = None
    options: List[OptionCreate] = Field(default_factory=list)

class QuestionUpdate(BaseModel):
    body_md: Optional[str] = Field(None, min_length=1)
    dimension_id: Optional[UUID] = None
    sequence: Optional[int] = Field(None, ge=0)
    block_label: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class DimensionCreate(BaseModel):
    name: DimensionType = Field(...)
    code: DimensionCode = Field(...)
    sequence: int = Field(..., ge=0)

class DimensionUpdate(BaseModel):
    name: Optional[DimensionType] = None
    code: Optional[DimensionCode] = None
    sequence: Optional[int] = Field(None, ge=0)


class AssessmentCreate(BaseModel):
    code: AssessmentType
    name: str = Field(..., min_length=1)
    version_int: int = Field(..., ge=0)
    # scoring_method: ScoringMethod
    dimensions: List[DimensionCreate] = Field(default_factory=list)
    questions: List[QuestionCreate] = Field(default_factory=list)

class AssessmentUpdate(BaseModel):
    code: Optional[AssessmentType] = None
    name: Optional[str] = Field(None, min_length=1)
    version_int: Optional[int] = Field(None, ge=0)
    # scoring_method: Optional[ScoringMethod] = None


class OptionRead(BaseModel):
    option_id: UUID
    label: str
    value: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class DimensionRead(BaseModel):
    dimension_id: UUID
    name: DimensionType
    code: DimensionCode
    sequence: int

    model_config = ConfigDict(from_attributes=True)

class QuestionRead(BaseModel):
    question_id: UUID
    body_md: str
    dimension_id: UUID
    sequence: int
    dimension: DimensionRead  # ✅ Correct — one-to-one relationship
    block_label: Optional[str]
    options: List[OptionRead]

    model_config = ConfigDict(from_attributes=True)

class AssessmentRead(BaseModel):
    assessment_id: UUID
    code: AssessmentType
    name: str
    version_int: int
    # scoring_method: ScoringMethod
    dimensions: List[DimensionRead]
    questions: List[QuestionRead]
    result_profiles: Optional[List[ResultProfileRead]] = None # ✅ now matches the DB relationship (a list)

    model_config = ConfigDict(from_attributes=True)

class AssessmentNamesRead(BaseModel):
    assessment_id: UUID
    code: AssessmentType
    name: str
    version_int: int
    # scoring_method: ScoringMethod

    model_config = ConfigDict(from_attributes=True)


class SessionCreate(BaseModel):
    assessment_id: UUID


class ResponseSubmit(BaseModel):
    question_id: UUID
    option_id: UUID
    value: int
    answered_at: datetime

class ResponseRead(BaseModel):
    response_id: UUID
    session_id: UUID
    question_id: UUID
    option_id: Optional[UUID]
    value: Optional[str]
    answered_at: datetime
    model_config = ConfigDict(from_attributes=True)

class SessionProfileRead(BaseModel):
    session_id: UUID
    profile_id: UUID
    confidence_pct: Optional[float]
    profile: ResultProfileRead

    model_config = ConfigDict(from_attributes=True)


# class SessionResult(BaseModel):
#     session_id: UUID
#     profile_id: UUID
#     confidence_pct: Optional[float]
#     profile: Optional[ResultProfileReadSim]

#     model_config = ConfigDict(from_attributes=True)



class SessionResult(BaseModel):
    session_id: UUID
    profile_id: Optional[UUID]
    confidence_pct: Optional[float]
    profile: Optional[ResultProfileReadSim]
    percent_correct: Optional[float] = None
    points_scored: Optional[int] = None
    points_max: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)



class SessionRead(BaseModel):
    session_id: UUID
    user_id: UUID
    assessment_id: UUID
    started_at: datetime
    completed_at: Optional[datetime]
    responses: Optional[List[ResponseRead]]
    session_profile: Optional[SessionProfileRead]

    model_config = ConfigDict(from_attributes=True)


class GradingService(BaseModel):
    total_questions: int
    correct_count: int
    percent_correct: float
    points_scored: int
    points_max: int

